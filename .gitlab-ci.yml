# This file is a template, and might need editing before it works on your project.
# You can copy and paste this template into a new `.gitlab-ci.yml` file.
# You should not add this template to an existing `.gitlab-ci.yml` file by using the `include:` keyword.
#
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Nodejs.gitlab-ci.yml

# Official framework image. Look for the different tagged releases at:
# https://hub.docker.com/r/library/node/tags/

# This folder is cached between builds
# https://docs.gitlab.com/ee/ci/yaml/index.html#cache
cache:
  paths:
    - node_modules/

stages:
  - build
  - run

variables:
  S3_BUCKET_PROD: ${BUCKET_PROD}

.yarn_build:
  image: node:latest
  script: |
    yarn # Install all dependencies
    CI=false && yarn build:${APP_ENV} # Build command
  artifacts:
    paths:
      - ./build

yarn_prod:
  extends: .yarn_build
  stage: build
  before_script:
    - export APP_ENV="production"
  only:
    - master

.deploy_aws:
  image: python:latest
  script: |
    pip install awscli #Install awscli tools
    echo "$S3_BUCKET"
    aws s3 sync ./build/ s3://${S3_BUCKET}

deploy_prod:
  extends: .deploy_aws
  stage: run
  needs: ["yarn_prod"]
  before_script:
    - export S3_BUCKET=${S3_BUCKET_PROD}
  only:
    - master

