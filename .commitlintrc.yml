extends:
  - "@commitlint/config-conventional"
rules:
  header-max-length: [1, "always", 100]
  type-enum:
    - 2
    - always
    - - chore
      - ci
      - feat
      - fix
      - docs
      - style
      - refactor
      - perf
      - test
      - revert
help: |
  **Possible types**:
  `chore`:    Change build process, tooling or dependencies.
  `ci`:       Changes to our CI configuration files and scripts (example scopes: Travis, Circle, BrowserStack, SauceLabs)
  `feat`:     Adds a new feature.
  `fix`:      Solves a bug.
  `docs`:     Adds or alters documentation.
  `style`:    Improves formatting, white-space.
  `refactor`: Rewrites code without feature, performance or bug changes.
  `perf`:     Improves performance.
  `test`:     Adds or modifies tests.
  `revert`:   Changes that reverting other changes
