@import "~react-datepicker/dist/react-datepicker.min.css";
@import "./custom/Datepicker.scss";

@tailwind components;
@tailwind base;
@tailwind utilities;

body {
  margin: 0;
  font-family: "DM Sans", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.bg-media {
  background: repeating-conic-gradient(rgb(234 234 234) 0%, rgb(216 216 216) 25%, transparent 0%, transparent 50%) 50% center/20px 20px;
}

.calendar-item {
  &:hover {
    background-color: #2A7871;

    * {
      color: white;
    }
  }
}

.dropdown:hover .dropdown-menu {
  display: block;
}

// .rbc-time-slot:hover {
//   background-color: gray;
//   z-index: 9999;
// }

table.sinh_hieu tr:nth-child(odd) {
  background-color: #dcefdc7e;
}

table.service tr:nth-child(odd) {
  background-color: #dcefdc7e;
}

table.service {
  text-align: left;
}

table.service .price {
  text-align: right;
}

.tagify--focus {
  border: 1px solid green !important;
}

.tagify__tag {
  width: calc(100% - 10px);
}

.tagify {
  border-color: green !important;
  min-height: 55px;
  --tag-inset-shadow-size: 2em !important;
}

.tagify[disabled] {
  opacity: 1 !important;
}

.jsoneditor-menu {
  background-color: #416045 !important;
  border-bottom: 1px solid #416045 !important;
}

.accordion__content{
  max-height: 0em;
  transition: all 0.4s cubic-bezier(0.865, 0.14, 0.095, 0.87);
  padding-top: 10px;
}
input[name='panel']:checked ~ .accordion__content {
  /* Get this as close to what height you expect */
  max-height: 100000px;
}

.rbc-event-content {
  font-size: 11px;
}

pre {
  font-family: "DM Sans", sans-serif;
}

.rsc {
  height: 100% !important;
  width: calc(min(100vw, 400px));
  position: fixed;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.rsc-container {
  width: -webkit-fill-available !important; 
  height: 100% !important;
  position: fixed;
  right: 0;
  bottom: 0;
}

.multiselect-container{
  width: 100%;
  position: relative;
  display: flex;  
  flex-direction: column;
  align-items: center;
  overflow: visible;
  vertical-align: middle;
}
.multiselect-label{
  font-weight: bold;
  font-size: 14px;
  margin: 0 5px;
  margin-bottom: 5px;
}
.multiselect-data-container{
  position: relative;
  overflow: visible;
  vertical-align: middle;
  width: 100%;
}
.multiselect-button{
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  border: 1px solid #ccc;
  padding: 6px 12px;
  margin-bottom: 0;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.42857143;
  color: #333;
  cursor: pointer;
}

.multiselect-text{
  border: 0;
  padding: 3px 0;
  width: 100%;
}

.multiselect-button input,
.multiselect-button b{
  pointer-events: none;
}

.multiselect-list{
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  margin: 0;
  padding: 0;
  max-height: 250px;
  width: 100%;
  background-color: #fff;
  border: 1px solid rgba(0,0,0,.15);
  box-shadow: 0 6px 12px rgba(0,0,0,.175);
  font-size: 14px;
  text-align: left;
  list-style: none;
  background-clip: padding-box;
  border-radius: 4px;
  list-style-type: none;
  overflow: auto;
  z-index: 1000;
}

.multiselect-item-label{
  display:block;
  height: 100%;
  border: 1px solid transparent;  
}

.multiselect-item-label--active{
  border: 1px solid #b7b7b7;  
}

.multiselect-clear{
  padding: 0 5px; 
}

.carret{
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 0;
  vertical-align: middle;
  border-top: 4px solid;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
}

.multiselect-item-label--select-all{
  font-weight: bold;
  border-bottom: 1px solid #b7b7b7;
  padding: 5px;
}

.dbpedia {
  width: 100%;
}

.rsc-content {
  height: calc(100% - 112px) !important;
}

.rsc-cs {
  padding: 0 !important;
  background-color: transparent !important;
  box-shadow: none !important;
  justify-content: start !important;
}

.rsc-container {
  border-radius: 0 !important;
}

.rsc-ts-bubble {
  -webkit-animation: gZmgDU 0.3s ease forwards;
  animation: gZmgDU 0.3s ease forwards;
  background: #426044;
  border-radius: 18px 18px 18px 0;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.15);
  color: #fff;
  display: inline-block;
  font-size: 14px;
  max-width: 50%;
  margin: 0 0 10px 0;
  overflow: hidden;
  position: relative;
  padding: 12px;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  -webkit-transform-origin: bottom left;
  -ms-transform-origin: bottom left;
  transform-origin: bottom left;
}

.rsc-ts-bot {
  webkit-align-items: flex-end;
    -webkit-box-align: flex-end;
    -ms-flex-align: flex-end;
    align-items: flex-end;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
}

.rsc-ts-image-container {
  display: inline-block;
    -webkit-order: 0;
    -ms-flex-order: 0;
    order: 0;
    padding: 6px;
}

.rsc-ts-image {
  -webkit-animation: gZmgDU 0.3s ease forwards;
    animation: gZmgDU 0.3s ease forwards;
    border-radius: 50% 50% 0 50%;
    box-shadow: rgba(0,0,0,0.15) 0px 1px 2px 0px;
    height: 40px;
    min-width: 40px;
    padding: 3px;
    -webkit-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
    -webkit-transform-origin: bottom right;
    -ms-transform-origin: bottom right;
    transform-origin: bottom right;
    width: 40;
}

#omi_sdk_pi5 {
  bottom: 50px !important;
}

// #omi_sdk_oss {
//   left: 296px;
// }