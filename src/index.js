import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { Provider } from "react-redux";
import { <PERSON>rowser<PERSON>outer as Router } from "react-router-dom";
import { PersistGate } from "redux-persist/integration/react";

import store, { persistor } from "state/store";

import "styles/index.scss";
import App from "./App";
import reportWebVitals from "./reportWebVitals";

const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(
  // <React.StrictMode>
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <Router>
          <App />
        </Router>
      </PersistGate>
    </Provider>
  // </React.StrictMode>
);
// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
