import { useCallback, useEffect, useState } from "react"
import { use<PERSON><PERSON>, Controller } from "react-hook-form"
import { useNavigate } from "react-router-dom"
import { yupResolver } from "@hookform/resolvers/yup"
import * as yup from "yup"
import classNames from "classnames"
import { toast } from "react-toastify"

import Button from "components/Button"
import Input from "components/Input"
import Select from "components/Select"
import Datepicker from "components/Datepicker"
import { CUSTOMER_TAG, GENDER } from "constants/Customer"
import provinceData from "constants/province.json"
import wardData from "constants/ward.json"
import { createNewUser, getListUsers, updateUser } from "services/api/users"
import { createNoOTP, getPatientByPhone } from "services/api/patient";
import { randomPassword } from "utils/string"
import { getErrorMessage } from "utils/error"
import {
  getPatientSource,
} from "services/api/patientSource";
import { formatStrapiArr, formatStrapiObj } from "utils/strapi";

const SOURCES = [
  {
    value: 'app',
    label: 'APP'
  },
  {
    value: 'web',
    label: 'WEB'
  },
  {
    value: 'app_be',
    label: 'App BE'
  },
  {
    value: 'other',
    label: 'Khác'
  }
];

const CustomersForm = ({ data, fromCheckIn, onUpdateGuestUserCheckin, onCloseModal, slotInfo }) => {
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)
  const [wardList, setWardList] = useState([])
  const [loadingCustomers, setLoadingCustomers] = useState(false)
  const [customersData, setCustomersData] = useState([])
  const [bookingHour, setBookingHour] = useState("");
  const [patientExist, setPatientExist] = useState(false);
  const [patientSource, setPatientSource] = useState();

  useEffect(() => {
    ; (async () => {
      try {
        const res = await getPatientSource()
        const data = formatStrapiArr(res?.data);
       
        let rs = data.map(r => {
          r.label = r.label;
          r.value = r.value;
          return r;
        })

        setPatientSource(rs);
      } catch (error) { }
    })()
  }, [])


  const validationSchema = yup.object({
    name: yup.string().required("Tên là bắt buộc"),
    phone: yup.string().required("Số điện thoại là bắt buộc"),
  })

  const {
    handleSubmit,
    control,
    setValue,
    getValues,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      code: data?.code || "",
      email: data?.email || "",
      name: data?.name || data?.full_name || "",
      gender: data?.gender || "",
      phone: data?.phone || "",
      birthday: !!data?.birthday ? new Date(data?.birthday) : null,
      address: {
        province: data?.address?.province || null,
        ward: data?.address?.ward || null,
        address: data?.address?.address || "",
      },
      source: data?.source ? {
        id: data?.source,
      } : null,
      customerTag:
        data?.customerTag === "referral"
          ? CUSTOMER_TAG.REFERRAL
          : data?.customerTag === "new"
            ? CUSTOMER_TAG.NEW_CUSTOMER
            : null,
      referral: data?.referral
        ? {
          value: data?.referral?.id,
          label: `${data?.referral?.firstName} ${data?.referral?.lastName} (${data?.referral?.code})`,
        }
        : null,
    },
  })

  // Convert province data to array format
  const provincesList = Object.values(provinceData)

  // init ward list based on selected province
  useEffect(() => {
    if (data?.address?.province) {
      const filteredWards = Object.values(wardData).filter(
        (ward) => ward.parent_code === data?.address?.province?.code
      )
      setWardList(
        filteredWards.map((ward) => ({
          value: ward.code,
          label: ward.name,
          ...ward
        }))
      )
    }
  }, [data?.address?.province])

  const provinceFormatted = () => {
    return provincesList.map((province) => ({
      value: province.code,
      label: province.name,
    }))
  }

  const bookingHours = () => {
    let result = [];
    for (let i = 0; i < 24; ++i) {
      result.push({
        value: `${i}:00`,
        label: `${i}:00`,
      });
      result.push({
        value: `${i}:30`,
        label: `${i}:30`,
      });
    }
    return result;
  }

  const handleSearchCustomer = useCallback((value) => {
    if (!value) return
    setLoadingCustomers(true)
    getListUsers(
      { pageSize: 1000 },
      {
        $or: [
          { firstName: { $containsi: value } },
          { lastName: { $containsi: value } },
          { code: { $containsi: value } },
        ],
      }
    )
      .then((res) => {
        if (res.data) {
          setCustomersData(
            res.data?.map((customer) => ({
              value: customer?.id,
              label: `${customer?.firstName} ${customer?.lastName} (${customer?.code})`,
            }))
          )
        }
        setLoadingCustomers(false)
      })
      .catch(() => {
        setLoadingCustomers(false)
      })
  }, [])

  const onSubmit = async (formData) => {
    try {
      setLoading(true)
      const payload = {
        ...formData,
        username: formData.email,
        referral: formData?.referral?.value,
        customerTag: formData.customerTag === CUSTOMER_TAG.REFERRAL ? "referral" : "new",
        patient_source: formData?.patient_source?.id,
      }
      if (data?.id) {
        await updateUser(data?.id, payload)
        toast.success("Customer updated successfully")
      } else {
        const password = randomPassword()
        const res = await createNoOTP(payload)
        if (res.data) {
          if (fromCheckIn) {
            onUpdateGuestUserCheckin(res.data.user)
          } else {
            navigate(-1)
            toast.success("Customer updated successfully")
          }
        }
      }
    } catch (error) {
      toast.error(getErrorMessage(error))
    } finally {
      setLoading(false)
    }
  }


  const handleSearchCustomerByPhone = (e) => {
    const toastId = toast.loading("Tìm khách hàng")
    getPatientByPhone(e.target.value)
      .then((res) => {
        if (res.data) {
          setPatientExist(true);
        }
        setLoadingCustomers(false)
      })
      .catch(() => {
        setPatientExist(false);
      })
      .then(() => {
        toast.dismiss(toastId);
      });
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <p>{JSON.stringify(slotInfo)}</p>
      <div className="space-y-6">
        <div className="grid grid-cols-2 sm:grid-cols-1 gap-6">
          <Controller
            name="name"
            control={control}
            render={({ field: { onChange, value } }) => (
              <Input
                onChange={onChange}
                value={value}
                name="name"
                label="Họ và tên"
                placeholder={"Nhập Họ và tên"}
                errors={errors?.name?.message}
              />
            )}
          />
          <div className="space-y-2">
            <label className="font-16 font-bold">Giới tính</label>
            <div className="grid grid-cols-2 gap-x-6">
              <Controller
                name="gender"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <>
                    {[GENDER.MALE, GENDER.FEMALE]?.map((gender) => (
                      <Button
                        key={gender}
                        onChange={onchange}
                        type="button"
                        className={classNames("w-full h-10 pl-6 !justify-start capitalize", {
                          "bg-primary text-white font-bold": value === gender,
                          "bg-primary/10 text-primary font-normal": value !== gender,
                        })}
                        onClick={() => setValue("gender", gender)}
                      >
                        {gender == "male" ? "Nam" : "Nữ"}
                      </Button>
                    ))}
                    {errors?.gender?.message && (
                      <p className="text-12 text-error mt-1">{errors?.gender?.message}</p>
                    )}
                  </>
                )}
              />
            </div>
          </div>
          {data && (
            <Controller
              name="code"
              control={control}
              render={({ field: { onChange, value } }) => (
                <Input
                  onChange={onChange}
                  value={value}
                  name="code"
                  label="Customer ID"
                  placeholder="Customer Id"
                  disabled
                />
              )}
            />
          )}
          <Controller
            name="email"
            control={control}
            render={({ field: { onChange, value } }) => (
              <Input
                onChange={onChange}
                value={value}
                name="email"
                label="Email"
                placeholder={"Nhập Email"}
                errors={errors?.email?.message}
              />
            )}
          />
          <div>
            <Controller
              name="phone"
              control={control}
              render={({ field: { onChange, value } }) => (
                <Input
                  onChange={onChange}
                  onBlur={handleSearchCustomerByPhone}
                  value={value}
                  name="phone"
                  label="Số điện thoại"
                  placeholder={"Nhập số điện thoại"}
                  errors={errors?.phone?.message}
                />
              )}
            />
            {patientExist && <p className="text-red">Số điện thoại đã tồn tại</p>}
          </div>
          <Controller
            name="birthday"
            control={control}
            render={({ field: { value, ref } }) => (
              <Datepicker
                label="Ngày sinh"
                value={value}
                onChange={(date) => {
                  // setValue("birthday", date)
                  setValue("birthday", Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate()))
                }}
                errors={errors?.birthday?.message}
              />
            )}
          />
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-1 gap-x-6">
          <div className="w-full">
            <Controller
              name="address.province"
              control={control}
              render={({ field: { value, ref } }) => (
                <Select
                  placeholder="Chọn tỉnh/thành phố"
                  label="Tỉnh/Thành phố"
                  name="address.province"
                  onChange={(e) => {
                    setValue(
                      "address.province",
                      { code: e.value, name: e.label },
                      { shouldDirty: true, shouldValidate: true }
                    )

                    // Filter wards based on selected province
                    const filteredWards = Object.values(wardData).filter(
                      (ward) => ward.parent_code === e.value
                    )
                    setWardList(
                      filteredWards.map((ward) => ({
                        value: ward.code,
                        label: ward.name,
                        ...ward
                      }))
                    )

                    setValue("address.ward", null, { shouldDirty: true })
                  }}
                  value={value && { value: value?.code, label: value?.name }}
                  options={provinceFormatted()}
                  errors={errors?.address?.province?.message}
                />
              )}
            />
          </div>
          <div className="w-full">
            <Controller
              name="address.ward"
              control={control}
              render={({ field: { value, ref } }) => (
                <Select
                  isDisabled={!getValues("address.province")}
                  placeholder="Chọn xã/phường/thị trấn"
                  label="Xã/Phường/Thị trấn"
                  name="address.ward"
                  onChange={(e) => {
                    setValue(
                      "address.ward",
                      { code: e.value, name: e.label },
                      { shouldDirty: true, shouldValidate: true }
                    )
                  }}
                  value={value && { value: value?.code, label: value?.name }}
                  options={wardList}
                  errors={errors?.address?.ward?.message}
                />
              )}
            />
          </div>
        </div>

        <Controller
          name="address.address"
          control={control}
          render={({ field: { onChange, value } }) => (
            <Input
              name="address.address"
              label="Địa chỉ tạm trú"
              placeholder={"Nhập địa chỉ tạm trú"}
              value={value}
              onChange={onChange}
              errors={errors?.address?.address?.message}
            />
          )}
        />
        <Controller
            name="address_cccd"
            control={control}
            render={({ field: { onChange, value } }) => (
              <Input
                onChange={onChange}
                value={value}
                name="address_cccd"
                label="Đia chỉ thường trú"
                placeholder={"Nhập đia chỉ thường trú"}
                errors={errors?.address_cccd?.message}
              />
            )}
          />
          <Controller
            name="cccd"
            control={control}
            render={({ field: { onChange, value } }) => (
              <Input
                onChange={onChange}
                value={value}
                name="cccd"
                label="CCCD"
                placeholder={"Nhập CCCD"}
                errors={errors?.cccd?.message}
              />
            )}
          />
        <Controller
            name="patient_source"
            control={control}
            render={({ field: { onChange, value, ref } }) => (
              <Select
                placeholder="Nguồn"
                label="Nguồn"
                name="patient_source"
                options={patientSource}
                value={value && patientSource?.find((s) => s.id === value.id)}
                onChange={(e) => {
                  setValue(
                    "patient_source",
                    { id: e.id },
                    {
                      shouldValidate: true,
                      shouldDirty: true,
                    }
                  )
                }}
                errors={errors?.category?.message}
              />
            )}
          />
        {/* <div className="grid grid-cols-2 gap-x-6">
          <div className="space-y-2">
            <label className="font-16 font-bold">Customer Tag</label>
            <div className="grid grid-cols-2 gap-x-6">
              <Controller
                name="customerTag"
                control={control}
                render={({ field: { value } }) => (
                  <>
                    {[CUSTOMER_TAG.NEW_CUSTOMER, CUSTOMER_TAG.REFERRAL]?.map((tag) => (
                      <Button
                        key={tag}
                        onChange={onchange}
                        type="button"
                        className={classNames("w-full h-14 pl-6 !justify-start capitalize", {
                          "bg-primary text-white font-bold": value === tag,
                          "bg-primary/10 text-primary font-normal": value !== tag,
                        })}
                        onClick={() =>
                          setValue("customerTag", tag, {
                            shouldDirty: true,
                            shouldValidate: true,
                          })
                        }
                      >
                        {tag}
                      </Button>
                    ))}
                    {errors?.customerTag?.message && (
                      <p className="text-12 text-error mt-1">{errors?.customerTag?.message}</p>
                    )}
                  </>
                )}
              />
            </div>
          </div>
          {getValues("customerTag") === CUSTOMER_TAG.REFERRAL && (
            <Controller
              name="referral"
              control={control}
              render={({ field: { value } }) => (
                <Select
                  onChange={(e) => {
                    setValue("referral", e, {
                      shouldValidate: true,
                      shouldDirty: true,
                    })
                  }}
                  onInputChange={handleSearchCustomer}
                  value={value}
                  name="referral"
                  label="Referral"
                  placeholder="Select Referral"
                  isLoading={loadingCustomers}
                  options={customersData}
                  errors={errors?.referral?.message}
                />
              )}
            />
          )}
        </div> */}
      </div>

      <div className="flex gap-x-4 mt-10">
        <Button disabled={patientExist} className="fill-primary" type="submit" loading={loading}>
          Lưu
        </Button>
        <Button
          btnType="outline"
          type="reset"
          onClick={() => (fromCheckIn ? onCloseModal() : navigate(-1))}
        >
          Huỷ
        </Button>
      </div>
    </form>
  )
}

export default CustomersForm
