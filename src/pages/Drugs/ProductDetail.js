import classNames from "classnames"
import dayjs from "dayjs"
import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { toast } from "react-toastify"
import { getStrapiMedia } from "utils/media"
import sumBy from "lodash/sumBy"
import { formatDate } from "utils/dateTime"
import { getErrorMessage } from "utils/error"
import Button from "components/Button"
import DataItem from "components/DataItem"
import Icon from "components/Icon"
import Tag from "components/Tag"
import Input from "components/Input"
import { BRAND_STATUS } from "constants/Brand"
import { updateDrugEndDate } from "services/api/drug"
import ProductDescription from "./components/ProductDescription"
import ProductImages from "./components/ProductImages"
import ProductInventory from "./components/ProductInventory"
import ProductVariants from "./components/ProductVariants"
import Price from "components/Price"
import { numberWithCommas } from "pages/Invoice/Components/InvoiceTable"

const ProductDetail = ({ data, onTogglePublish, onUpdateProduct }) => {
  const navigate = useNavigate()
  const [openProductDescriptionDrawer, setOpenProductDescriptionDrawer] = useState(false)
  const [openProductImagesDrawer, setOpenProductImagesDrawer] = useState(false)
  const [openProductVariantsDrawer, setOpenProductVariantsDrawer] = useState(false)
  const [openProductInventoryDrawer, setOpenProductInventoryDrawer] = useState(false)
  const [isEditingEndDate, setIsEditingEndDate] = useState(false)
  const [endDateValue, setEndDateValue] = useState(data?.endDate || "")
  const [loading, setLoading] = useState(false)

  const handleUpdateEndDate = async () => {
    if (!endDateValue) {
      toast.error("Vui lòng nhập ngày hết hạn")
      return
    }

    try {
      setLoading(true)
      await updateDrugEndDate(data?.id, endDateValue)

      // Update the local data
      const updatedData = { ...data, endDate: endDateValue }
      onUpdateProduct(updatedData)

      setIsEditingEndDate(false)
      toast.success("Cập nhật ngày hết hạn thành công")
    } catch (error) {
      toast.error(getErrorMessage(error))
    } finally {
      setLoading(false)
    }
  }

  const handleCancelEdit = () => {
    setEndDateValue(data?.endDate || "")
    setIsEditingEndDate(false)
  }

  return (
    <div className="my-10 w-full" id='customer-detail'>
      <div className="flex items-center gap-x-2">
        <div className="flex items-center flex-1 gap-x-4">
          {/* <div className="w-30">
            <img src={getStrapiMedia(data?.images?.[0])} alt="Product" />
          </div> */}
          <div className="flex-1">
            <p className="text-24 font-bold">{data?.code}</p>
            <p className="text-18 break-all">{data?.name}</p>
            <Tag
              className={classNames("mt-4 rounded-lg", {
                "bg-red": !data.publishedAt,
                "bg-green": data.publishedAt,
              })}
              name={data.publishedAt ? BRAND_STATUS.PUBLISHED : BRAND_STATUS.UNPUBLISHED}
            />
          </div>
        </div>
        {/* <div className="flex gap-x-2">
          <Button
            btnSize="auto"
            className="w-10 h-10"
            shape="circle"
            onClick={() => navigate(`/products/${data?.id}/edit`)}
          >
            <Icon name="edit" />
          </Button>
          <Button
            btnSize="auto"
            className={`w-10 h-10 ${data?.publishedAt ? "bg-red" : "bg-green"}`}
            shape="circle"
            onClick={onTogglePublish}
          >
            <Icon name="slash" />
          </Button>
        </div> */}
      </div>
      <div className="grid grid-cols-1 grid-flow-row mt-4 space-y-2">
        <DataItem icon="key" title="Product ID" value={data?.code} />


        <DataItem icon="3dcube" title="Tên thuốc" value={data?.label} />


        <DataItem
          icon="3d-square"
          title="Hoạt chất"
          value={data?.ingredient}
          valueClassName="capitalize"
        />  
        <div className="flex gap-x-2">
          <div className="flex items-center justify-center w-10.5 h-10.5 rounded-full bg-primary/10">
            <Icon name="3dcube" className="w-6 h-6 fill-primary" />
          </div>
          <div className="flex-1 overflow-x-hidden">
            <p className="text-12 font-bold text-secondary/[0.56]">Giá</p>
            <div style={{ whiteSpace: 'break-spaces' }} className={`text-16 whitespace-break-spaces`}>
              {numberWithCommas(data.price)}
            </div>
          </div>
        </div>
        <DataItem
          icon="bag"
          title="Tồn"
          value={data?.stock + " " + data?.unit}
        />
        <DataItem
          icon="tag-right"
          title="Loại"
          value={
            <div>
              <p>{data?.type}</p>
            </div>
          }
          valueClassName="capitalize"
        />

        <DataItem
          icon="calendar"
          title="Lần cuối cập nhật"
          value={
            <div>
              <p>{formatDate(data?.updatedAt, "DD/MM/YYYY")}</p>
            </div>
          }
          valueClassName="capitalize"
        />

        {/* End Date Section */}
        <div className="flex gap-x-2 items-start">
          <div className="flex items-center justify-center w-10.5 h-10.5 rounded-full bg-primary/10 mt-1">
            <Icon name="calendar" className="w-6 h-6 fill-primary" />
          </div>
          <div className="flex-1">
            <p className="text-12 font-bold text-secondary/[0.56] mb-2">Ngày hết hạn</p>
            {isEditingEndDate ? (
              <div className="space-y-3">
                <Input
                  type="date"
                  value={endDateValue}
                  onChange={(e) => setEndDateValue(e.target.value)}
                  placeholder="Chọn ngày hết hạn"
                  className="w-full"
                />
                <div className="flex items-center gap-x-2">
                  <Button
                    btnSize="sm"
                    className="bg-green hover:bg-green/80 text-white px-4 py-2"
                    onClick={handleUpdateEndDate}
                    loading={loading}
                    disabled={loading}
                  >
                    <Icon name="check" className="w-4 h-4 fill-white mr-1" />
                    Lưu
                  </Button>
                  <Button
                    btnSize="sm"
                    btnType="outline"
                    className="border-gray-300 text-gray-600 hover:bg-gray-50 px-4 py-2"
                    onClick={handleCancelEdit}
                    disabled={loading}
                  >
                    Hủy
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-x-2">
                  <span className={`text-16 font-medium ${
                    data?.endDate
                      ? (new Date(data.endDate) < new Date() ? 'text-red-600' : 'text-gray-900')
                      : 'text-gray-400'
                  }`}>
                    {data?.endDate ? formatDate(data?.endDate, "DD/MM/YYYY") : "Chưa có"}
                  </span>
                  {data?.endDate && new Date(data.endDate) < new Date() && (
                    <span className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full">
                      Hết hạn
                    </span>
                  )}
                </div>
                <Button
                  btnSize="auto"
                  className="w-8 h-8 bg-primary/10 hover:bg-primary/20 transition-colors"
                  shape="circle"
                  onClick={() => setIsEditingEndDate(true)}
                >
                  <Icon name="edit" className="w-4 h-4 fill-primary" />
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* <ProductImages
        images={data?.images}
        openDrawer={openProductImagesDrawer}
        onClose={() => setOpenProductImagesDrawer(false)}
      />
      <ProductVariants
        variants={data?.variants}
        openDrawer={openProductVariantsDrawer}
        onClose={() => setOpenProductVariantsDrawer(false)}
      />
      <ProductDescription
        detail={data}
        openDrawer={openProductDescriptionDrawer}
        onClose={() => setOpenProductDescriptionDrawer(false)}
      />
      <ProductInventory
        productId={data?.id}
        variants={data?.variants}
        inventory={sumBy(data?.variants, (variant) => parseInt(variant.inventory))}
        inventoryHistories={data?.inventory_histories}
        onUpdate={onUpdateProduct}
        openDrawer={openProductInventoryDrawer}
        onClose={() => setOpenProductInventoryDrawer(false)}
      /> */}
    </div>
  )
}

export default ProductDetail
