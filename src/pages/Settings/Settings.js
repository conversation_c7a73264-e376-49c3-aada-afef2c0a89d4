import Page from "components/Page"
import Icon from "../../components/Icon"
import { useNavigate } from "react-router-dom"

const SETTINGS = [
  {
    icon: "truck",
    title: "<PERSON>ảng viết tắt",
    url: "/settings/abbreviation",
  },
  // {
  //   icon: "briefcase",
  //   title: "Role",
  //   url: "/settings/role",
  // },
  {
    icon: "coin",
    title: "Lý do giảm giá",
    url: "/settings/discount-reason",
  },
  {
    icon: "coin",
    title: "Đổi mật khẩu",
    url: "/settings/change-password",
  },
  {
    icon: "coin",
    title: "<PERSON><PERSON> ký",
    url: "/settings/signature",
  },
  {
    icon: "coin",
    title: "<PERSON><PERSON><PERSON><PERSON> khách hàng",
    url: "/settings/patient-source",
  },
  {
    icon: "coin",
    title: "Cài đặt chung",
    url: "/settings/global-setting",
  },
]

const Settings = () => {
  const navigate = useNavigate()

  return (
    <Page title="Cài đặt">
      <div className="grid sm:grid-cols-1 grid-cols-4 gap-6">
        {SETTINGS.map((item, index) => (
          <button
            key={index}
            type="button"
            onClick={() => navigate(item.url)}
            className="flex flex-col items-center bg-gray2 py-8 rounded-xl"
          >
            <Icon name={item.icon} className="fill-primary w-12 h-12" />
            <b className="font-bold text-24 text-primary mt-4">{item.title}</b>
          </button>
        ))}
      </div>
    </Page>
  )
}

export default Settings
