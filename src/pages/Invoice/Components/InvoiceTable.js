import classNames from "classnames"
import { useCallback, useMemo } from "react"

import Table from "components/Table"
import Tag from "components/Tag"
import { CATEGORY_STATUS } from "constants/Category"
import { formatDate } from "utils/dateTime"

export function numberWithCommas(x) {
  return x?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
}

const InvoiceTable = ({ data, activeRow, loading, pageCount, onClickRow, fetchData }) => {
  const handleClickRow = useCallback(
    (row) => {
      if (row?.id === activeRow?.id) {
        onClickRow(null)
      } else {
        onClickRow(row)
      }
    },
    [activeRow?.id, onClickRow]
  )

  const columns = useMemo(() => {
    const defaultColumns = [
      {
        Header: "Mã khách hàng",
        accessor: (originalRow) => (
          <p
            className={`font-bold my-2 ${
              originalRow?.id === activeRow?.id ? "text-white" : "text-primary"
            }`}
          >
           
            {originalRow?.patient?.uid}
          </p>
        ),
        collapse: true,
        width: 120,
      },
      {
        Header: "Tên <PERSON>h<PERSON>ch hàng",
        accessor: (originalRow) => <p>{originalRow?.patient?.full_name?.toUpperCase()}</p>,
        collapse: true,
        width: 160,
      },
      {
        Header: "Mã phiếu thu",
        accessor: (originalRow) => <p> {originalRow?.idu}</p>,
        collapse: true,
        width: 160,
      },
    ]
    if (activeRow) return defaultColumns
    return [
      ...defaultColumns,
      // {
      //   Header: "Category",
      //   accessor: (originalRow) =>
      //     originalRow.categories.map((c, index) => (
      //       <span key={index} className="block">
      //         {c.title.en}
      //       </span>
      //     )),
      //   collapse: true,
      //   width: 150,
      // },
      {
        Header: "Ngày khám bệnh",
        accessor: (originalRow) => {
          return (
            <div className="flex">
              <span className="">{formatDate(originalRow.createdAt, "DD/MM/YYYY")}</span>
            </div>
          )
        },
        collapse: true,
        width: 150,
      },
      {
        Header: "Tổng cộng",
        accessor: (originalRow) => {
          return (
            <div className="flex">
              <span className="">{numberWithCommas(originalRow.data?.total)}</span>
            </div>
          )
        },
        collapse: true,
        width: 100,
      },
      {
        Header: "Trạng thái",
        accessor: (originalRow) => (
          <Tag
            className={classNames({
              "bg-red": !originalRow.publishedAt,
              "bg-green": originalRow.publishedAt,
            })}
            name={originalRow.publishedAt ? "Đã thanh toán" : "Nháp"}
          />
        ),
        align: "right",
        collapse: true,
        width: 100,
      },
    ]
  }, [activeRow])

  return (
    <Table
      className="mt-2"
      columns={columns}
      data={data}
      fetchData={fetchData}
      loading={loading}
      pageCount={pageCount}
      hidePagination={activeRow}
      activeRow={activeRow}
      onClickRow={handleClickRow}
    />
  )
}

export default InvoiceTable
