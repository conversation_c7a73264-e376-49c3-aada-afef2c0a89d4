import React, { useState, useEffect, useRef, useCallback } from "react"
import { Controller } from "react-hook-form"

import Input from "components/Input"
import Select from "components/Select"
import { getListDrugs } from "services/api/drug"
import { formatStrapiArr, formatStrapiObj } from "utils/strapi"
import Icon from "components/Icon"
import { getValue } from "@testing-library/user-event/dist/utils"
import { getListPatients } from "services/api/patient"
import { uploadMedia } from "services/api/mediaLibrary"
import { toast } from "react-toastify"
import { getStrapiMedia } from "utils/media"

function removeVietnameseTones(str) {
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a")
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e")
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i")
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o")
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u")
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y")
  str = str.replace(/đ/g, "d")
  str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A")
  str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E")
  str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I")
  str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O")
  str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U")
  str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y")
  str = str.replace(/Đ/g, "D")
  // Some system encode vietnamese combining accent as individual utf-8 characters
  // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
  str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, "") // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
  str = str.replace(/\u02C6|\u0306|\u031B/g, "") // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
  // Remove extra spaces
  // Bỏ các khoảng trắng liền nhau
  str = str.replace(/ + /g, " ")
  str = str.trim()
  // Remove punctuations
  // Bỏ dấu câu, kí tự đặc biệt
  str = str.replace(
    /!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g,
    " "
  )
  return str
}

const PrescriptionFormItem = ({
  index,
  item,
  control,
  setValue,
  getValues,
  errors,
  handleUpdateAmount,
  remove,
  allDrugs,
}) => {
  const [listDrugs, setListDrugs] = useState([])
  const [loading, setLoading] = useState(false)
  const ref = useRef()

  console.log('item', item.image)

  const handleSearchPatients = (value) => {
    // setLoadingCustomers(true)
    setLoading(true);
    getListPatients(
      { pageSize: 1000 },
      {
        $or: [
          { uid: { $containsi: value } },
          { phone: { $containsi: value } },
          { full_name_i: { $containsi: removeVietnameseTones(value) } },
        ],
      }
    )
      .then((res) => {
        const patients = formatStrapiArr(res.data);
        if (patients) {
          setListDrugs(
            patients?.map((customer) => ({
              value: customer?.id,
              label: `${customer?.full_name}`,
            }))
          )
        }
        setLoading(false)
      })
      .catch(() => {
        setLoading(false)
      })
  }

  const onFinish = useCallback(
    async (id, files) => {
      let payload = cloneDeep(testResults)
      if (payload?.[id]) {
        payload[id] = [...payload[id], ...files]
      } else {
        payload = {
          ...payload,
          [id]: files,
        }
      }
      window.location.reload();
      // await updateMedicalRecord(medicalRecordId, {
      //   testResults: payload,
      // })
      // await fetchData()
    },
    []
  )
  
  const uploadAssets = useCallback(
    async (id, e) => {
      const toastId = toast.loading("Đang tải lên")
      try {
        const uploadedFiles = [...e.target.files]
        const promises = uploadedFiles?.map((file) => {
          const formData = new FormData()
          formData.append("files", file)
          formData.append("ref", "api::patient-source.patient-source")
          formData.append("refId", item.uid)
          formData.append("field", "image")
          return uploadMedia(formData)
        })
        const response = await Promise.all(promises)
        
        const files = flatten(response?.map((item) => item.data))
        if (files) {
          onFinish(id, files)
        }
      } catch (error) {
        // toast.error(getErrorMessage(error));
      } finally {
        toast.dismiss(toastId)
        window.location.reload();
      }
    },
    [onFinish]
  )

  return (
    <div className="flex flex-col gap-2" key={item.id}>
      <div className="grid grid-cols-3 gap-4">
        <Controller
          name={`relationship[${index}].label`}
          label="Tên"
          control={control}
          render={({ field: { onChange, value } }) => (
            <Input

              label={'Tên'}
              onChange={e => {
                setValue(`relationship[${index}].label`, e.target.value, {
                  shouldValidate: true,
                  shouldDirty: true,
                })
              }}
              value={value ?? item.label}
              name="code"
            />
          )}
        />
        <Controller
          name={`relationship[${index}].value`}
          label="Code"
          control={control}
          render={({ field: { onChange, value } }) => (
            <Input

              label={'Code'}
              onChange={e => {
                setValue(`relationship[${index}].value`, e.target.value, {
                  shouldValidate: true,
                  shouldDirty: true,
                })
              }}
              value={value ?? item.value}
              name="code"
            />
          )}
        />
        <div>
        <p>Logo</p>
        <div className="inline-flex items-center justify-center rounded-xl bg-background p-4 relative border-primary border-1">
          <img src={getStrapiMedia(item.image?.data?.attributes)} width={"100px"}/>
          <input
            ref={ref}
            type="file"
            className="h-full w-full opacity-0 cursor-pointer absolute z-20"
          onChange={(e) => uploadAssets(item.id, e)}
          // multiple
          />
          <p>Tải lên</p>
        </div>
        </div>
        {/* <div className="col-span-1 flex gap-2 justify-between">
          <button type="button" className="mb-4" onClick={() => remove(index)}>
            <Icon name="trash" className="fill-red" />
          </button>
        </div> */}
      </div>
    </div>
  )
}

export default PrescriptionFormItem
