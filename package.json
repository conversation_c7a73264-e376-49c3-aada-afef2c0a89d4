{"name": "echomedi-cms", "version": "1.0.0", "private": true, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^37.0.1", "@ckeditor/ckeditor5-react": "^6.0.0", "@hookform/resolvers": "2.9.3", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@material-ui/lab": "^4.0.0-alpha.61", "@reduxjs/toolkit": "^1.8.3", "@tailwindcss/line-clamp": "^0.4.0", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "@uiw/react-md-editor": "^3.14.5", "@yaireo/tagify": "^4.17.6", "autoprefixer": "^10.4.7", "axios": "^0.27.2", "chart.js": "^3.9.1", "classnames": "^2.3.1", "date-fns": "^2.29.3", "dayjs": "^1.11.3", "dotenv": "^16.3.1", "firebase": "^9.22.0", "lodash": "^4.17.21", "moment": "^2.29.4", "nodemailer": "^6.8.0", "postcss": "^8.4.14", "qs": "^6.11.0", "react": "^18.2.0", "react-avatar": "^5.0.1", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.5.2", "react-chartjs-2": "^4.3.1", "react-datepicker": "^4.8.0", "react-device-detect": "^2.2.2", "react-dom": "^18.2.0", "react-email-editor": "^1.7.11", "react-hook-form": "7.33.0", "react-inlinesvg": "^3.0.0", "react-markdown": "^8.0.3", "react-paginate": "^8.1.3", "react-redux": "^8.0.2", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "react-select": "^5.3.2", "react-simple-chatbot": "^0.6.1", "react-table": "^7.8.0", "react-toastify": "^9.0.7", "read-excel-file": "^5.5.3", "recharts": "^2.1.12", "redux-persist": "^6.0.0", "sass": "^1.52.3", "shortid": "^2.2.16", "signature_pad": "^4.1.6", "socket.io": "^4.6.1", "socket.io-client": "^4.6.1", "styled-components": "^5.3.5", "swiper": "^8.4.4", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss": "^3.1.4", "tinycolor2": "^1.4.2", "twilio": "^4.19.0", "twilio-video": "^2.28.1", "unsplash-js": "^7.0.15", "uuid": "^9.0.0", "web-vitals": "^2.1.4", "yup": "^0.32.11"}, "scripts": {"start": "react-scripts start", "build": "sh -ac '. ./.env.${REACT_APP_ENV}; react-scripts build'", "build:production": "REACT_APP_ENV=production npm run build", "test": "react-scripts test", "eject": "react-scripts eject", "postinstall": "husky install"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@commitlint/cli": "^17.0.3", "@commitlint/config-conventional": "^17.0.3", "husky": "^8.0.1", "lint-staged": "^13.0.3"}, "lint-staged": {"*.js": ["eslint --fix"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}}